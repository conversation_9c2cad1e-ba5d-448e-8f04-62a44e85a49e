<template>
  <div class="control-panel transform-panel">
    <!-- 面板标题 -->
    <div class="panel-title">编辑模式</div>
    <div class="sub-title">{{ transformTitle }}</div>

    <div class="transform-options">
      <div
        class="transform-option"
        :class="{ active: activeTransform === EditMode.TRANSFORM }"
        @click="setActiveTransform(EditMode.TRANSFORM)"
      >
        <div class="option-icon">
          <img
            :src="activeTransform === EditMode.TRANSFORM ? transformActiveIcon : transformIcon"
            alt="整体结构调整图标"
            class="option-img"
          />
        </div>
      </div>
      <div
        class="transform-option"
        :class="{ active: activeTransform === EditMode.DETAIL }"
        @click="setActiveTransform(EditMode.DETAIL)"
      >
        <div class="option-icon">
          <img
            :src="activeTransform === EditMode.DETAIL ? detailActiveIcon : detailIcon"
            alt="精细编辑图标"
            class="option-img"
          />
        </div>
      </div>
      <div
        class="transform-option"
        :class="{ active: activeTransform === EditMode.MIRROR }"
        @click="setActiveTransform(EditMode.MIRROR)"
      >
        <div class="option-icon">
          <img
            :src="activeTransform === EditMode.MIRROR ? mirrorActiveIcon : mirrorIcon"
            alt="镜像拷贝图标"
            class="option-img"
          />
        </div>
      </div>
    </div>

    <!-- 变换模式下的选项 -->
    <div v-if="activeTransform === EditMode.TRANSFORM" class="checkbox-section">
      <div class="checkbox-item">
        <div class="section-label">镜像编辑</div>
        <el-checkbox v-model="mirrorEditModel" checked class="custom-checkbox"></el-checkbox>
      </div>
      <div class="checkbox-item">
        <div class="section-label">连桥编辑</div>
        <el-checkbox v-model="bridgeEditModel" checked class="custom-checkbox"></el-checkbox>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { EditMode } from '@/contexts/VeneerEditorContext'
import transformIcon from '@/assets/images/transform.png'
import transformActiveIcon from '@/assets/images/transform-active.png'
import detailIcon from '@/assets/images/detailControl.png'
import detailActiveIcon from '@/assets/images/detailControl-active.png'
import mirrorIcon from '@/assets/images/mirror.png'
import mirrorActiveIcon from '@/assets/images/mirror-active.png'

// Props
const props = defineProps({
  activeTransform: {
    type: String,
    required: true
  },
  transformTitle: {
    type: String,
    required: true
  },
  mirrorEdit: {
    type: Boolean,
    required: true
  },
  bridgeEdit: {
    type: Boolean,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:activeTransform', 'update:mirrorEdit', 'update:bridgeEdit'])

// 计算属性，用于双向绑定
const mirrorEditModel = computed({
  get: () => props.mirrorEdit,
  set: (value) => emit('update:mirrorEdit', value)
})

const bridgeEditModel = computed({
  get: () => props.bridgeEdit,
  set: (value) => emit('update:bridgeEdit', value)
})

// 设置当前变换选项
function setActiveTransform(option: EditMode) {
  emit('update:activeTransform', option)
}
</script>

<style scoped>
.transform-panel {
  padding-top: 10px;
  padding-bottom: 10px;
  background: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 12px 15px;
  border-radius: 8px;
  margin-bottom: 10px;
  background-color: rgba(255, 245, 235, 0.9);
}

.panel-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.transform-options {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 5px 0;
}

.transform-option {
  width: 70px;
  height: 40px;
  background-color: #f0f2f5;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  padding: 5px;
}

.transform-option.active {
  background-color: #7f9ec4;
  color: white;
}

.option-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.option-img {
  width: auto;
  object-fit: contain;
  border-radius: 6px;
}

.sub-title {
  font-size: 14px;
  color: #333;
  margin: 6px 0;
}

.section-label {
  font-size: 15px;
  color: #333;
  font-weight: normal;
}

.checkbox-section {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-bottom: 0;
}

.checkbox-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:deep(.el-checkbox) {
  height: 20px;
}
</style>
