<template>
  <div class="veneer-page" :style="{ height: `calc(100vh - ${headerHeight}px)` }">
    <!-- 底图导入与交互层 -->
    <el-upload
      class="upload-btn"
      :show-file-list="false"
      :before-upload="handleBeforeUpload"
      accept="image/*"
    >
      <el-button type="primary">导入底图</el-button>
    </el-upload>
    <el-button class="save-btn" type="success" @click="handleSave">保存牙齿数据</el-button>

    <!-- 全屏画布区域 -->
    <SmileCanvas
      v-if="smileImgObj && mouthImgObj"
      ref="canvasRef"
      :showMidline="false"
      :showSmileFrame="true"
      :showMouthImage="false"
      :interactiveTeeth="true"
      :isTexturePage="false"
      :customBgImgObj="customBgImgObj"
      v-model:customBgTransform="bgState"
      :hideSmileImage="true"
    />

    <!-- 浮动控制面板 -->
    <div class="floating-controls">
      <TransformPanel
        v-model:activeTransform="activeTransform"
        :transformTitle="transformTitle"
        v-model:mirrorEdit="mirrorEdit"
        v-model:bridgeEdit="bridgeEdit"
      />
      <DatabasePanel :templates="templates" v-model:currentTemplate="currentTemplate" />
      <ResetPanel @reset="handleReset" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { shallowRef, ref } from 'vue'
import { useVeneerEditorContext } from '@/contexts/VeneerEditorContext'
import { useControlPanels } from '@/composables/veneer/useControlPanels'
import { useResetSmile } from '@/composables/veneer/useResetSmile'
import { useWindowResize } from '@/composables/useWindowResize'
import { useTeethStore } from '@/store/teeth'
import { useFrameRelativePositionStore } from '@/store/frameRelativePosition'
import SmileCanvas from '@/components/SmileCanvas.vue'
import TransformPanel from '@/components/veneer/TransformPanel.vue'
import DatabasePanel from '@/components/veneer/DatabasePanel.vue'
import ResetPanel from '@/components/veneer/ResetPanel.vue'
import { ElMessage } from 'element-plus'

const editorContext = useVeneerEditorContext()
const headerHeight = editorContext.headerHeight
const smileImgObj = editorContext.smileImgObj
const mouthImgObj = editorContext.mouthImgObj

const { mirrorEdit, bridgeEdit, activeTransform, transformTitle, templates, currentTemplate } =
  useControlPanels()

const canvasRef = shallowRef<InstanceType<typeof SmileCanvas> | null>(null)
const { handleReset } = useResetSmile(canvasRef, currentTemplate, editorContext)
useWindowResize(() => {
  if (canvasRef.value && canvasRef.value.canvasContainerRef) {
    editorContext.updateStageSize(canvasRef.value.canvasContainerRef)
  }
})
const teethStore = useTeethStore()
const frameRelativePositionStore = useFrameRelativePositionStore()

// 底图相关
const customBgImgObj = ref<HTMLImageElement | null>(null)
const customBgImgUrl = ref<string>('')
const bgState = ref({
  x: 600,
  y: 400,
  scale: 1,
  rotation: 0,
  dragging: false,
  offsetX: 0,
  offsetY: 0
})

function handleBeforeUpload(file: File) {
  const reader = new FileReader()
  reader.onload = (e) => {
    const img = new window.Image()
    img.onload = () => {
      customBgImgObj.value = img
      customBgImgUrl.value = img.src
      bgState.value = {
        x: 600,
        y: 400,
        scale: 1,
        rotation: 0,
        dragging: false,
        offsetX: 0,
        offsetY: 0
      }
    }
    img.src = e.target?.result as string
  }
  reader.readAsDataURL(file)
  return false
}

function handleSave() {
  // 优先导出 teethStore.customToothShapes，否则导出当前模板的 toothShapes
  let data = teethStore.customToothShapes
  if (!data || Object.keys(data).length === 0) {
    data = teethStore.customToothShapes || {}
  }
  if (!data || Object.keys(data).length === 0) {
    ElMessage.error('没有可导出的牙齿数据！')
    return
  }
  const json = JSON.stringify(data, null, 2)
  const blob = new Blob([json], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'teeth-shapes.json'
  a.click()
  URL.revokeObjectURL(url)
  ElMessage.success('已导出JSON文件')
}
</script>

<style scoped>
.veneer-page {
  position: relative;
  width: 100vw;
  height: 100vh;
  background: #f7f8fa;
  overflow: hidden;
}
.upload-btn {
  position: absolute;
  top: 24px;
  left: 24px;
  z-index: 20;
}
.save-btn {
  position: absolute;
  top: 24px;
  left: 140px;
  z-index: 20;
}
.floating-controls {
  position: absolute;
  top: 80px;
  left: 20px;
  z-index: 30;
  width: 280px;
}
</style>
